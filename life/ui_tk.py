from __future__ import annotations
import tkinter as tk
import random
from .model import LifeModel

class LifeTk:
    def __init__(self, rows: int = 40, cols: int = 60, cell_size: int = 12, speed_ms: int = 120):
        self.model = LifeModel(rows, cols)
        self.cell_size = cell_size
        self.speed_ms = speed_ms
        self.running = False

        self.root = tk.Tk()
        self.root.title("Life UI (click to toggle; r=run/pause, space=step, c=clear, n=random, q=quit)")
        w, h = cols * cell_size, rows * cell_size
        self.canvas = tk.Canvas(self.root, width=w, height=h, bg="white", highlightthickness=1, highlightcolor="black")
        self.canvas.pack()

        # Make canvas focusable for keyboard events
        self.canvas.focus_set()

        self._draw_grid_lines(rows, cols)
        self._draw_cells()  # Initial draw

        # Add some initial pattern so user sees something immediately
        self._add_initial_pattern()

        self.canvas.bind("<Button-1>", self._on_click)
        self.canvas.bind("<KeyPress-space>", self._on_step)
        self.canvas.bind("<KeyPress-r>", self._on_toggle_run)
        self.canvas.bind("<KeyPress-c>", self._on_clear)
        self.canvas.bind("<KeyPress-n>", self._on_random)
        self.canvas.bind("<KeyPress-q>", lambda e: self.root.destroy())

    def _draw_grid_lines(self, rows: int, cols: int):
        cs = self.cell_size
        # Draw more visible grid lines
        for r in range(rows + 1):
            y = r * cs
            self.canvas.create_line(0, y, cols * cs, y, fill="#cccccc", width=1)
        for c in range(cols + 1):
            x = c * cs
            self.canvas.create_line(x, 0, x, rows * cs, fill="#cccccc", width=1)

    def _draw_cells(self):
        cs = self.cell_size
        self.canvas.delete("cell")
        for (r, c) in self.model.alive:
            x0, y0 = c * cs, r * cs
            x1, y1 = x0 + cs, y0 + cs
            self.canvas.create_rectangle(x0, y0, x1, y1, fill="black", outline="", tags="cell")

    def _on_click(self, event):
        c = event.x // self.cell_size
        r = event.y // self.cell_size
        if 0 <= r < self.model.rows and 0 <= c < self.model.cols:
            self.model.toggle(r, c)
            self._draw_cells()

    def _on_step(self, _event=None):
        if not self.running:
            self.model.step()
            self._draw_cells()

    def _on_toggle_run(self, _event=None):
        self.running = not self.running
        if self.running:
            self._run_loop()

    def _on_clear(self, _event=None):
        self.model.clear()
        self._draw_cells()

    def _on_random(self, _event=None):
        self.model.randomize(0.2)
        self._draw_cells()

    def _run_loop(self):
        if self.running:
            self.model.step()
            self._draw_cells()
            self.root.after(self.speed_ms, self._run_loop)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    LifeTk().run()

